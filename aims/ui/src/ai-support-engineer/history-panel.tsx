import { faSearch, faFire, faComment, faCheck } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Input, Skeleton, Tooltip, Tag } from 'antd';
import classNames from 'classnames';
import { useMemo, useState, useCallback } from 'react';

import { AIConversation } from '@/lib/apiclient/organization/v1/organization_pb';
import { timestampToDate } from '@/utils';

interface HistoryPanelProps {
  onSelectChat: (conversationId: string) => void;
  historyChats: AIConversation[];
  isLoading?: boolean;
  activeConversationId?: string;
}

export const HistoryPanel = ({
  onSelectChat,
  historyChats = [],
  isLoading = false,
  activeConversationId
}: HistoryPanelProps) => {
  const [searchText, setSearchText] = useState('');

  const filteredChats = useMemo(() => {
    if (!searchText) return historyChats;
    const lowerSearchText = searchText.toLowerCase();
    return historyChats.filter((chat) =>
      (chat.title || 'New Conversation').toLowerCase().includes(lowerSearchText)
    );
  }, [historyChats, searchText]);

  const handleChatSelect = useCallback(
    (chat: AIConversation) => {
      onSelectChat(chat.id);
    },
    [onSelectChat]
  );

  const renderLoadingState = () => (
    <div className='px-4'>
      {[1, 2, 3, 4, 5].map((key) => (
        <div key={key} className='py-4 border-b border-[#f0f0f0]'>
          <Skeleton
            active
            paragraph={{ rows: 1, width: ['60%'] }}
            title={{ width: '80%' }}
            className='opacity-60'
          />
        </div>
      ))}
    </div>
  );

  return (
    <div className='overflow-hidden bg-white h-full'>
      <div className='sticky top-0 z-[1] px-4 py-4 border-b border-[#f0f0f0] text-sm font-semibold'>
        <h3 className='m-0'>Conversation History</h3>
      </div>

      <div className='px-4 py-3 border-b border-[#f0f0f0]'>
        <Input
          prefix={<FontAwesomeIcon icon={faSearch} className='text-gray-400' />}
          placeholder='Search conversations...'
          value={searchText}
          onChange={(e) => setSearchText(e.target.value)}
          className='w-full'
          allowClear
        />
      </div>
      <div className='overflow-y-auto h-[calc(100%-110px)]'>
        {searchText && (
          <div className='bg-[#f9fafb] p-2'>
            {filteredChats.length ? `Results (${filteredChats.length})` : 'No Results'}
          </div>
        )}
        {isLoading ? (
          renderLoadingState()
        ) : (
          <>
            {filteredChats.map((chat) => (
              <div
                key={chat.id}
                className={`p-[16px] border-b border-[#f0f0f0] cursor-pointer ${activeConversationId === chat.id ? 'bg-[#eaedf1]' : 'hover:bg-[#f9fafb]'}`}
                onClick={() => handleChatSelect(chat)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    handleChatSelect(chat);
                  }
                }}
                role='button'
                tabIndex={0}
              >
                <div className='flex flex-col'>
                  <div className='text-gray-600 text-sm font-semibold'>
                    <Tooltip title={chat.incident ? 'incident' : 'conversation'}>
                      <FontAwesomeIcon
                        className={classNames('mr-2', {
                          'text-red-500': chat.incident && !chat.incident.resolvedAt,
                          'text-green-500': chat.incident && chat.incident.resolvedAt,
                          'text-gray-400': !chat.incident
                        })}
                        icon={
                          (chat.incident && chat.incident.resolvedAt && faCheck) ||
                          (chat.incident && faFire) ||
                          faComment
                        }
                      />
                    </Tooltip>{' '}
                    {chat.title || 'New Conversation'}
                  </div>
                  <div className='flex justify-between items-center'>
                    <div className='text-[#999] text-xs mt-1'>
                      {timestampToDate(chat.lastUpdateTime).toLocaleString()}
                    </div>
                  </div>
                  {chat.incident && (
                    <>
                      {[
                        { field: chat.incident.summary, title: 'Summary' },
                        { field: chat.incident.rootCause, title: 'Root Cause' },
                        { field: chat.incident.resolution, title: 'Resolution' }
                      ]
                        .filter(({ field }) => field)
                        .map(({ field, title }) => (
                          <div key={field} className='text-xs text-gray-500 mt-2'>
                            <strong>{title}:</strong>{' '}
                            <Tooltip title={field}>
                              <span className='line-clamp-3'>{field}</span>
                            </Tooltip>
                          </div>
                        ))}
                      {(chat.incident.runbooks || []).length > 0 && (
                        <div className='flex flex-wrap gap-2 mt-2 text-sm text-gray-500'>
                          <strong>Runbooks:</strong>{' '}
                          {chat.incident.runbooks.map((item) => (
                            <Tag key={item}>{item}</Tag>
                          ))}
                        </div>
                      )}
                    </>
                  )}
                </div>
              </div>
            ))}
            {searchText && !filteredChats.length && (
              <div className='px-1 text-[#6b7280] text-center wrap-break-word'>
                No conversations found matching "{searchText}"
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};
